// 简化版本测试
// 普通粒子类
class Particle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    this.color = color(random(255), random(255), random(255));
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 爱心形状粒子类
class HeartParticle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.createHeartVelocity();
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 300;
    this.color = color(random(200, 255), random(50, 150), random(100, 200));
  }
  
  createHeartVelocity() {
    let t = random(TWO_PI);
    let scale = random(3, 8);
    let x = 16 * pow(sin(t), 3);
    let y = -(13 * cos(t) - 5 * cos(2*t) - 2 * cos(3*t) - cos(4*t));
    this.vel = createVector(x * scale / 30, y * scale / 30);
    this.vel.add(random(-1, 1), random(-1, 1));
    let speed = this.vel.mag();
    if (speed > 0) {
      this.vel.setMag(random(2, 6));
    }
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 全局变量
let particles = [];
let gravity;
let heartsToDraw = [];
let lastClickTime = 0;
let score = 0;
let combo = 0;
let comboTimer = 0;

function setup() {
  createCanvas(windowWidth, windowHeight);
  gravity = createVector(0, 0.05);
  background(0);
  
  // 初始化数组
  particles = [];
  heartsToDraw = [];
}

function draw() {
  background(0, 25);
  
  // 绘制临时爱心
  for (let i = heartsToDraw.length - 1; i >= 0; i--) {
    let heart = heartsToDraw[i];
    let currentSize = heart.size * (1 + (20 - heart.lifetime) / 20);
    drawHeart(heart.x, heart.y, currentSize);
    
    heart.lifetime--;
    if (heart.lifetime <= 0) {
      heartsToDraw.splice(i, 1);
    }
  }
  
  // 鼠标点击时创建爱心烟花效果
  if (mouseIsPressed && millis() - lastClickTime > 100) {
    lastClickTime = millis();
    
    // 连击系统
    if (millis() - comboTimer < 2000) {
      combo++;
      if (combo > 5) combo = 5;
    } else {
      combo = 1;
    }
    comboTimer = millis();
    
    // 计算分数
    let baseScore = 10;
    let comboBonus = combo * 2;
    score += baseScore + comboBonus;
    
    // 添加临时爱心显示
    heartsToDraw.push({
      x: mouseX,
      y: mouseY,
      size: 2 + combo * 0.2,
      lifetime: 20
    });
    
    // 创建爱心烟花效果
    let particleCount = 60 + combo * 10;
    for (let i = 0; i < particleCount; i++) {
      let p = new HeartParticle(mouseX, mouseY);
      particles.push(p);
    }
    
    // 额外创建一些普通粒子
    for (let i = 0; i < 10 + combo; i++) {
      let p = new Particle(mouseX, mouseY);
      p.vel.mult(0.3);
      particles.push(p);
    }
  }
  
  // 更新和显示粒子
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();
    
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }
  
  // 显示游戏信息
  fill(255);
  noStroke();
  textSize(16);
  text("💗 分数: " + score, 20, 30);
  text("🎯 连击: " + combo + "x", 20, 55);
  text("✨ 粒子: " + particles.length, 20, 80);
}

// 绘制爱心形状
function drawHeart(x, y, size) {
  push();
  translate(x, y);
  scale(size);
  
  fill(255, 50, 100, 180);
  stroke(255, 100, 150, 200);
  strokeWeight(2);
  
  beginShape();
  for (let i = 0; i < TWO_PI; i += 0.05) {
    let px = 16 * pow(sin(i), 3);
    let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i));
    vertex(px, py);
  }
  endShape(CLOSE);
  
  pop();
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}

function keyPressed() {
  if (key === ' ') {
    particles = [];
    heartsToDraw = [];
    background(0);
  }
}
