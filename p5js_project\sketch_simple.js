// 简化版本测试
// 普通粒子类
class Particle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    this.color = color(random(255), random(255), random(255));
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 爱心形状粒子类
class HeartParticle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.createHeartVelocity();
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 300;
    this.color = color(random(200, 255), random(50, 150), random(100, 200));
  }
  
  createHeartVelocity() {
    let t = random(TWO_PI);
    let scale = random(3, 8);
    let x = 16 * pow(sin(t), 3);
    let y = -(13 * cos(t) - 5 * cos(2*t) - 2 * cos(3*t) - cos(4*t));
    this.vel = createVector(x * scale / 30, y * scale / 30);
    this.vel.add(random(-1, 1), random(-1, 1));
    let speed = this.vel.mag();
    if (speed > 0) {
      this.vel.setMag(random(2, 6));
    }
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 星星类
class Star {
  constructor() {
    this.pos = createVector(random(width), random(height));
    this.brightness = random(100, 255);
    this.twinkleSpeed = random(0.02, 0.05);
    this.size = random(1, 3);
  }

  update() {
    this.brightness = 150 + sin(millis() * this.twinkleSpeed) * 100;
  }

  show() {
    fill(255, 255, 200, this.brightness);
    noStroke();
    ellipse(this.pos.x, this.pos.y, this.size);
  }
}

// 气球类
class Balloon {
  constructor() {
    this.pos = createVector(random(width), height + 50);
    this.vel = createVector(random(-0.5, 0.5), random(-1, -2));
    this.r = random(30, 50);
    this.color = color(random(255), random(255), random(255));
    this.bobOffset = random(TWO_PI);
    this.bobSpeed = random(0.02, 0.05);
  }

  update() {
    this.pos.add(this.vel);
    this.pos.x += sin(millis() * this.bobSpeed + this.bobOffset) * 0.3;
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);

    fill(this.color);
    stroke(255);
    strokeWeight(2);
    ellipse(0, 0, this.r * 2, this.r * 2.2);

    stroke(100);
    strokeWeight(1);
    line(0, this.r * 1.1, 0, this.r * 1.8);

    pop();
  }

  isOffScreen() {
    return this.pos.y < -this.r;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    return d < this.r;
  }
}

// 全局变量
let particles = [];
let gravity;
let heartsToDraw = [];
let balloons = [];
let stars = [];
let lastClickTime = 0;
let score = 0;
let combo = 0;
let comboTimer = 0;
let balloonsPoppedCount = 0;

function setup() {
  createCanvas(windowWidth, windowHeight);
  gravity = createVector(0, 0.05);
  background(0);

  // 初始化数组
  particles = [];
  heartsToDraw = [];
  balloons = [];
  stars = [];

  // 初始化背景星星
  for (let i = 0; i < 50; i++) {
    stars.push(new Star());
  }
}

function draw() {
  background(0, 25);

  // 绘制星星
  for (let star of stars) {
    star.update();
    star.show();
  }

  // 生成气球
  if (frameCount % 240 === 0 && balloons.length < 3) {
    balloons.push(new Balloon());
  }

  // 更新和绘制气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    balloons[i].update();
    balloons[i].show();

    if (balloons[i].isOffScreen()) {
      balloons.splice(i, 1);
    }
  }

  // 绘制临时爱心
  for (let i = heartsToDraw.length - 1; i >= 0; i--) {
    let heart = heartsToDraw[i];
    let currentSize = heart.size * (1 + (20 - heart.lifetime) / 20);
    drawHeart(heart.x, heart.y, currentSize);

    heart.lifetime--;
    if (heart.lifetime <= 0) {
      heartsToDraw.splice(i, 1);
    }
  }
  
  // 鼠标点击时创建爱心烟花效果
  if (mouseIsPressed && millis() - lastClickTime > 100) {
    lastClickTime = millis();

    // 检查是否点击了气球
    let balloonClicked = false;
    for (let i = balloons.length - 1; i >= 0; i--) {
      if (balloons[i].isClicked(mouseX, mouseY)) {
        balloonClicked = true;
        balloonsPoppedCount++;

        // 气球被击中的奖励
        let balloonBonus = 25 + combo * 5;
        score += balloonBonus;

        // 创建气球爆炸效果
        for (let j = 0; j < 30; j++) {
          let p = new Particle(balloons[i].pos.x, balloons[i].pos.y);
          p.color = balloons[i].color;
          particles.push(p);
        }

        balloons.splice(i, 1);
        break;
      }
    }

    // 如果没有点击气球，则创建普通烟花
    if (!balloonClicked) {
      // 连击系统
      if (millis() - comboTimer < 2000) {
        combo++;
        if (combo > 5) combo = 5;
      } else {
        combo = 1;
      }
      comboTimer = millis();

      // 计算分数
      let baseScore = 10;
      let comboBonus = combo * 2;
      score += baseScore + comboBonus;

      // 添加临时爱心显示
      heartsToDraw.push({
        x: mouseX,
        y: mouseY,
        size: 2 + combo * 0.2,
        lifetime: 20
      });

      // 创建爱心烟花效果
      let particleCount = 60 + combo * 10;
      for (let i = 0; i < particleCount; i++) {
        let p = new HeartParticle(mouseX, mouseY);
        particles.push(p);
      }

      // 额外创建一些普通粒子
      for (let i = 0; i < 10 + combo; i++) {
        let p = new Particle(mouseX, mouseY);
        p.vel.mult(0.3);
        particles.push(p);
      }
    }
  }
  
  // 更新和显示粒子
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();
    
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }
  
  // 显示游戏信息
  fill(255);
  noStroke();
  textSize(16);
  text("💗 分数: " + score, 20, 30);
  text("🎯 连击: " + combo + "x", 20, 55);
  text("✨ 粒子: " + particles.length, 20, 80);
  text("🎈 气球: " + balloonsPoppedCount, 20, 105);

  // 连击提示
  if (combo >= 3) {
    let comboText = "";
    let comboColor = color(255, 200, 100);
    if (combo >= 5) {
      comboText = "🌟 完美连击！";
      comboColor = color(255, 100, 255);
    } else if (combo >= 4) {
      comboText = "⭐ 超神连击！";
      comboColor = color(100, 255, 255);
    } else {
      comboText = "🔥 连击中！";
    }

    fill(comboColor);
    textSize(20);
    text(comboText, width/2 - 60, height - 50);
  }
}

// 绘制爱心形状
function drawHeart(x, y, size) {
  push();
  translate(x, y);
  scale(size);
  
  fill(255, 50, 100, 180);
  stroke(255, 100, 150, 200);
  strokeWeight(2);
  
  beginShape();
  for (let i = 0; i < TWO_PI; i += 0.05) {
    let px = 16 * pow(sin(i), 3);
    let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i));
    vertex(px, py);
  }
  endShape(CLOSE);
  
  pop();
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}

function keyPressed() {
  if (key === ' ') { // 空格键 - 清除所有粒子
    particles = [];
    heartsToDraw = [];
    background(0);
  } else if (key === 'r' || key === 'R') { // R键 - 随机位置发射爱心烟花
    let x = random(width);
    let y = random(height * 0.7);
    createHeartFirework(x, y);
  } else if (key === 'c' || key === 'C') { // C键 - 屏幕中央发射爱心烟花
    createHeartFirework(width/2, height/2);
  } else if (key === 's' || key === 'S') { // S键 - 重置分数
    score = 0;
    combo = 0;
    comboTimer = 0;
    balloonsPoppedCount = 0;
  }
}

// 创建爱心烟花的函数
function createHeartFirework(x, y) {
  // 添加临时爱心显示
  heartsToDraw.push({
    x: x,
    y: y,
    size: 3,
    lifetime: 25
  });

  // 创建爱心烟花粒子
  for (let i = 0; i < 100; i++) {
    let p = new HeartParticle(x, y);
    particles.push(p);
  }

  // 添加装饰粒子
  for (let i = 0; i < 20; i++) {
    let p = new Particle(x, y);
    p.vel.mult(0.5);
    particles.push(p);
  }
}
