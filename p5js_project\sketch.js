// 普通粒子类
class Particle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    // 随机速度，让烟花效果更丰富
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    // 随机颜色
    this.color = color(random(255), random(255), random(255));
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    // 设置颜色和透明度
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 气球类
class Balloon {
  constructor() {
    // 确保width和height已经定义
    let canvasWidth = width || windowWidth || 800;
    let canvasHeight = height || windowHeight || 600;

    this.pos = createVector(random(canvasWidth), canvasHeight + 50);
    this.vel = createVector(random(-0.5, 0.5), random(-1, -2));
    this.r = random(30, 50);
    this.color = color(random(255), random(255), random(255));
    this.points = 25;
    this.bobOffset = random(TWO_PI);
    this.bobSpeed = random(0.02, 0.05);
  }

  update() {
    this.pos.add(this.vel);
    // 添加轻微的摆动效果
    this.pos.x += sin(millis() * this.bobSpeed + this.bobOffset) * 0.3;
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);

    // 绘制气球
    fill(this.color);
    stroke(255);
    strokeWeight(2);
    ellipse(0, 0, this.r * 2, this.r * 2.2);

    // 绘制气球线
    stroke(100);
    strokeWeight(1);
    line(0, this.r * 1.1, 0, this.r * 1.8);

    pop();
  }

  isOffScreen() {
    return this.pos.y < -this.r;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    return d < this.r;
  }
}

// 道具类
class PowerUp {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.vel = createVector(random(-1, 1), random(-1, 1));
    this.r = 20;
    this.lifetime = 300; // 5秒生命周期
    this.type = random(['double', 'mega', 'slow']);
    this.bobOffset = random(TWO_PI);
  }

  update() {
    this.pos.add(this.vel);
    this.lifetime--;
    // 轻微摆动
    this.pos.y += sin(millis() * 0.05 + this.bobOffset) * 0.2;
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);

    // 根据类型绘制不同的道具
    let alpha = map(this.lifetime, 0, 300, 50, 255);

    if (this.type === 'double') {
      fill(255, 215, 0, alpha); // 金色
      stroke(255, 255, 0, alpha);
      strokeWeight(2);
      star(0, 0, this.r * 0.7, this.r, 5);

      fill(0, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(10);
      text("2X", 0, 0);
    } else if (this.type === 'mega') {
      fill(255, 100, 255, alpha); // 紫色
      stroke(255, 150, 255, alpha);
      strokeWeight(2);
      ellipse(0, 0, this.r * 2);

      fill(255, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(8);
      text("MEGA", 0, 0);
    } else if (this.type === 'slow') {
      fill(100, 255, 255, alpha); // 青色
      stroke(150, 255, 255, alpha);
      strokeWeight(2);
      rect(-this.r/2, -this.r/2, this.r, this.r, 5);

      fill(0, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(8);
      text("SLOW", 0, 0);
    }

    pop();
  }

  isFinished() {
    return this.lifetime <= 0;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    return d < this.r;
  }
}

// 星星类
class Star {
  constructor() {
    // 确保width和height已经定义
    let canvasWidth = width || windowWidth || 800;
    let canvasHeight = height || windowHeight || 600;

    this.pos = createVector(random(canvasWidth), random(canvasHeight));
    this.brightness = random(100, 255);
    this.twinkleSpeed = random(0.02, 0.05);
    this.size = random(1, 3);
  }

  update() {
    this.brightness = 150 + sin(millis() * this.twinkleSpeed) * 100;
  }

  show() {
    fill(255, 255, 200, this.brightness);
    noStroke();
    ellipse(this.pos.x, this.pos.y, this.size);
  }
}

// 爱心形状粒子类
class HeartParticle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    // 爱心形状的速度分布
    this.createHeartVelocity();
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 300; // 增加生命周期让粒子存活更久
    // 丰富多彩的爱心颜色主题
    let colorType = floor(random(4)); // 随机选择颜色类型
    switch(colorType) {
      case 0: // 粉红色系
        this.color = color(random(255, 255), random(50, 150), random(100, 200));
        break;
      case 1: // 红色系
        this.color = color(random(200, 255), random(0, 50), random(0, 50));
        break;
      case 2: // 紫红色系
        this.color = color(random(200, 255), random(0, 100), random(100, 200));
        break;
      case 3: // 金色系（特殊效果）
        this.color = color(random(255, 255), random(150, 255), random(0, 100));
        break;
    }
  }
  
  // 创建爱心形状的速度分布
  createHeartVelocity() {
    // 使用参数方程生成爱心形状的速度分布
    let t = random(TWO_PI);
    let scale = random(3, 8); // 增加规模范围让爱心更大
    // 爱心参数方程: x = 16sin³(t), y = 13cos(t) - 5cos(2t) - 2cos(3t) - cos(4t)
    let x = 16 * pow(sin(t), 3);
    let y = -(13 * cos(t) - 5 * cos(2*t) - 2 * cos(3*t) - cos(4*t));
    this.vel = createVector(x * scale / 30, y * scale / 30); // 调整缩放比例

    // 添加一些随机性，使烟花效果更自然
    this.vel.add(random(-1, 1), random(-1, 1));

    // 根据位置调整速度，让爱心形状更清晰
    let speed = this.vel.mag();
    if (speed > 0) {
      // 让粒子速度更均匀
      this.vel.setMag(random(2, 6));
    }
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    // 设置颜色和透明度
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 全局变量
let particles = [];
let gravity;
let heartsToDraw = []; // 用于跟踪爱心绘制
let lastClickTime = 0; // 记录上次点击时间，防止过于频繁的点击
let score = 0; // 爱心收集分数
let combo = 0; // 连击计数
let comboTimer = 0; // 连击计时器
let isAutoFireworkPaused = false; // 自动烟花暂停状态

// 新增游戏元素
let balloons = []; // 气球数组
let powerUps = []; // 道具数组
let stars = []; // 背景星星
let gameMode = "freeplay"; // 游戏模式：freeplay 或 timeattack
let timeLeft = 60; // 时间模式剩余时间
let gameTimer = 0; // 游戏计时器
let activeEffect = { type: null, until: 0 }; // 当前激活的特效
let feverMode = false; // 狂热模式
let feverUntil = 0; // 狂热模式结束时间
let backgroundHue = 0; // 背景色相
let balloonsPoppedCount = 0; // 气球击破数量

// 音效系统
let audioContext;
let soundEnabled = true;
let achievements = {
  firstClick: false,
  combo3: false,
  combo5: false,
  score100: false,
  score500: false,
  superFirework: false,
  balloonPopper: false, // 新成就：气球射手
  feverMaster: false, // 新成就：狂热大师
  timeAttacker: false // 新成就：时间挑战者
};
let currentTheme = 0; // 0: 粉色主题, 1: 彩虹主题, 2: 金色主题
let lastFrameTime = 0;
let fps = 0;

// UI按钮
let buttons = [];
let showUI = true;

// 辅助函数：绘制星形
function star(x, y, radius1, radius2, npoints) {
  let angle = TWO_PI / npoints;
  let halfAngle = angle / 2.0;
  beginShape();
  for (let a = 0; a < TWO_PI; a += angle) {
    let sx = x + cos(a) * radius2;
    let sy = y + sin(a) * radius2;
    vertex(sx, sy);
    sx = x + cos(a + halfAngle) * radius1;
    sy = y + sin(a + halfAngle) * radius1;
    vertex(sx, sy);
  }
  endShape(CLOSE);
}

// 初始化UI按钮
function initButtons() {
  // 确保width和height已经定义
  let canvasWidth = width || windowWidth || 800;
  let canvasHeight = height || windowHeight || 600;

  buttons = [
    { x: canvasWidth - 200, y: canvasHeight - 120, w: 80, h: 30, text: "重置", action: "reset" },
    { x: canvasWidth - 110, y: canvasHeight - 120, w: 80, h: 30, text: "主题", action: "theme" },
    { x: canvasWidth - 200, y: canvasHeight - 80, w: 80, h: 30, text: "音效", action: "sound" },
    { x: canvasWidth - 110, y: canvasHeight - 80, w: 80, h: 30, text: "模式", action: "mode" },
    { x: canvasWidth - 200, y: canvasHeight - 40, w: 170, h: 30, text: "显示/隐藏UI", action: "toggleUI" }
  ];
}

// 绘制渐变背景
function drawBackground() {
  // 创建简单的渐变背景
  backgroundHue = (backgroundHue + 0.2) % 360;

  for (let y = 0; y < height; y++) {
    let inter = map(y, 0, height, 0, 1);
    // 使用RGB颜色而不是HSB
    let r1 = 5 + sin(backgroundHue * 0.01) * 10;
    let g1 = 5 + sin(backgroundHue * 0.01 + 2) * 10;
    let b1 = 15 + sin(backgroundHue * 0.01 + 4) * 15;

    let r2 = 15 + sin(backgroundHue * 0.01 + 1) * 20;
    let g2 = 15 + sin(backgroundHue * 0.01 + 3) * 20;
    let b2 = 30 + sin(backgroundHue * 0.01 + 5) * 30;

    let c1 = color(r1, g1, b1);
    let c2 = color(r2, g2, b2);
    let c = lerpColor(c1, c2, inter);
    stroke(c);
    line(0, y, width, y);
  }

  // 绘制星星
  for (let star of stars) {
    star.update();
    star.show();
  }
}

// 音效系统函数
function initAudio() {
  try {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  } catch (e) {
    console.log('Web Audio API not supported');
  }
}

function playClickSound(frequency = 800, duration = 0.1) {
  if (!soundEnabled || !audioContext) return;

  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  } catch (e) {
    console.log('Error playing sound');
  }
}

function playComboSound(level) {
  if (!soundEnabled) return;

  // 根据连击等级播放不同音效
  switch(level) {
    case 3:
      playClickSound(1000, 0.2); // 三连击
      setTimeout(() => playClickSound(1200, 0.2), 100);
      break;
    case 5:
      playClickSound(800, 0.3); // 五连击
      setTimeout(() => playClickSound(1000, 0.3), 150);
      setTimeout(() => playClickSound(1200, 0.3), 300);
      break;
  }
}

function playSuperSound() {
  if (!soundEnabled) return;

  // 超级烟花音效
  playClickSound(600, 0.4);
  setTimeout(() => playClickSound(800, 0.3), 200);
  setTimeout(() => playClickSound(1000, 0.3), 400);
  setTimeout(() => playClickSound(1200, 0.2), 600);
}

// 主题系统
function getThemeColor(baseColor) {
  switch(currentTheme) {
    case 0: // 粉色主题
      return baseColor;
    case 1: // 彩虹主题
      return color(random(255), random(255), random(255));
    case 2: // 金色主题
      return color(random(200, 255), random(150, 255), random(0, 100));
    default:
      return baseColor;
  }
}

// 绘制爱心形状
function drawHeart(x, y, size, rotation = 0, isAchievement = false, message = "") {
  push();
  translate(x, y);
  rotate(rotation); // 添加旋转效果
  scale(size);

  if (isAchievement) {
    // 成就通知显示
    fill(255, 255, 100, 200);
    stroke(255, 200, 0, 255);
    strokeWeight(3);
    rect(-150, -30, 300, 60, 10);

    fill(0);
    noStroke();
    textSize(16);
    textAlign(CENTER, CENTER);
    text(message, 0, 0);
  } else {
    // 创建渐变色爱心效果 - 支持主题系统
    let heartColor = getThemeColor(color(255, 50, 100, 180)); // 粉红色
    let strokeColor = getThemeColor(color(255, 100, 150, 200)); // 浅粉色描边

    fill(heartColor);
    stroke(strokeColor);
    strokeWeight(2);

    beginShape();
    for (let i = 0; i < TWO_PI; i += 0.05) { // 更平滑的曲线
      let px = 16 * pow(sin(i), 3);
      let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i));
      vertex(px, py);
    }
    endShape(CLOSE);

    // 添加爱心内部的小亮点效果
    let highlightColor = getThemeColor(color(255, 150, 200, 100));
    fill(highlightColor);
    noStroke();
    ellipse(0, -2, 6, 6); // 爱心上方的亮点
  }

  pop();
}

function setup() {
  try {
    createCanvas(windowWidth, windowHeight);
    gravity = createVector(0, 0.05);

    // 初始化所有数组
    particles = [];
    balloons = [];
    powerUps = [];
    stars = [];
    heartsToDraw = [];
    buttons = [];

    // 设置初始背景
    background(0);

    console.log("Canvas created:", width, height);

    // 初始化音频系统
    try {
      initAudio();
    } catch (e) {
      console.log("Audio init failed:", e);
    }

    // 初始化背景星星
    try {
      for (let i = 0; i < 50; i++) {
        stars.push(new Star());
      }
      console.log("Stars initialized:", stars.length);
    } catch (e) {
      console.log("Stars init failed:", e);
    }

    // 初始化UI按钮
    try {
      initButtons();
      console.log("Buttons initialized:", buttons.length);
    } catch (e) {
      console.log("Buttons init failed:", e);
    }

    // 监听用户交互来激活音频上下文
    const startAudio = () => {
      if (audioContext && audioContext.state === 'suspended') {
        audioContext.resume();
      }
      document.removeEventListener('click', startAudio);
      document.removeEventListener('touchstart', startAudio);
    };
    document.addEventListener('click', startAudio);
    document.addEventListener('touchstart', startAudio);

    console.log("Setup completed successfully");
  } catch (e) {
    console.error("Setup failed:", e);
  }
}

function draw() {
  try {
    // 简单的黑色背景
    background(0, 25);

    // 绘制星星（如果存在）
    if (stars && stars.length > 0) {
      for (let star of stars) {
        try {
          star.update();
          star.show();
        } catch (e) {
          console.log("单个星星绘制错误:", e);
        }
      }
    }
  } catch (e) {
    console.log("Draw函数开始部分错误:", e);
    // 如果出错，至少保证黑色背景
    background(0);
  }
  
  // 生成气球（确保数组已初始化）
  try {
    if (balloons && frameCount % 240 === 0 && balloons.length < 5) { // 每4秒生成一个气球，最多5个
      balloons.push(new Balloon());
    }

    // 更新和绘制气球
    if (balloons) {
      for (let i = balloons.length - 1; i >= 0; i--) {
        balloons[i].update();
        balloons[i].show();

        if (balloons[i].isOffScreen()) {
          balloons.splice(i, 1);
        }
      }
    }
  } catch (e) {
    console.log("气球处理错误:", e);
  }

  // 生成道具（较少频率）
  if (powerUps && frameCount % 600 === 0 && powerUps.length < 2) { // 每10秒生成一个道具，最多2个
    let x = random(50, width - 50);
    let y = random(50, height - 50);
    powerUps.push(new PowerUp(x, y));
  }

  // 更新和绘制道具
  if (powerUps) {
    for (let i = powerUps.length - 1; i >= 0; i--) {
      powerUps[i].update();
      powerUps[i].show();

      if (powerUps[i].isFinished()) {
        powerUps.splice(i, 1);
      }
    }
  }

  // 检查狂热模式
  if (feverMode && millis() > feverUntil) {
    feverMode = false;
  }

  // 绘制临时爱心和成就通知
  for (let i = heartsToDraw.length - 1; i >= 0; i--) {
    let heart = heartsToDraw[i];

    if (heart.isAchievement) {
      // 成就通知 - 位置固定在屏幕中央，不随生命周期变化
      drawHeart(heart.x, heart.y, heart.size, heart.rotation, true, heart.message);
    } else {
      // 普通爱心 - 让爱心在生命周期中逐渐变大并旋转
      let currentSize = heart.size * (1 + (20 - heart.lifetime) / 20);
      let currentRotation = heart.rotation + (20 - heart.lifetime) * 0.1;
      drawHeart(heart.x, heart.y, currentSize, currentRotation);
    }

    heart.lifetime--;
    if (heart.lifetime <= 0) {
      heartsToDraw.splice(i, 1);
    }
  }
  
  // 鼠标点击时创建爱心烟花效果
  if (mouseIsPressed && millis() - lastClickTime > 100) { // 防止过于频繁的点击
    lastClickTime = millis();

    // 检查是否点击了气球
    let balloonClicked = false;
    if (balloons) {
      for (let i = balloons.length - 1; i >= 0; i--) {
        if (balloons[i].isClicked(mouseX, mouseY)) {
          balloonClicked = true;
          balloonsPoppedCount++;

          // 气球被击中的奖励
          let balloonBonus = 25 + combo * 5;
          score += balloonBonus;

          // 创建气球爆炸效果
          for (let j = 0; j < 30; j++) {
            let p = new Particle(balloons[i].pos.x, balloons[i].pos.y);
            p.color = balloons[i].color;
            particles.push(p);
          }

          // 播放气球爆炸音效
          playClickSound(1200, 0.2);

          balloons.splice(i, 1);
          break;
        }
      }
    }

    // 检查是否点击了道具
    let powerUpClicked = false;
    if (powerUps) {
      for (let i = powerUps.length - 1; i >= 0; i--) {
        if (powerUps[i].isClicked(mouseX, mouseY)) {
          powerUpClicked = true;
          activatePowerUp(powerUps[i].type);
          powerUps.splice(i, 1);
          break;
        }
      }
    }

    // 如果没有点击特殊物品，则创建普通烟花
    if (!balloonClicked && !powerUpClicked) {
      // 播放点击音效
      playClickSound(800 + combo * 50, 0.15);

      // 连击系统
      if (millis() - comboTimer < 2000) { // 2秒内点击算连击
        combo++;
        if (combo > 5) combo = 5; // 最大连击数限制

        // 连击音效
        if (combo === 3) playComboSound(3);
        else if (combo === 5) {
          playComboSound(5);
          // 五连击触发狂热模式
          if (!feverMode) {
            feverMode = true;
            feverUntil = millis() + 7000; // 7秒狂热模式
            showAchievement("🌟 狂热模式激活！7秒超级加成！");
          }
        }
      } else {
        combo = 1;
      }
      comboTimer = millis();

      // 成就检查
      checkAchievements();

      // 根据连击数和特效计算分数
      let baseScore = 10;
      let comboBonus = combo * 2;
      let feverBonus = feverMode ? 5 : 0;
      let effectBonus = (activeEffect.type === 'double' && millis() < activeEffect.until) ? baseScore + comboBonus : 0;

      score += baseScore + comboBonus + feverBonus + effectBonus;

      // 添加一个临时爱心显示
      heartsToDraw.push({
        x: mouseX,
        y: mouseY,
        size: 2 + combo * 0.2, // 连击时爱心变大
        lifetime: 20,
        rotation: random(TWO_PI) // 添加随机旋转
      });

      // 创建爱心烟花效果 - 根据连击数和特效调整粒子数量
      let particleCount = 60 + combo * 10;
      if (feverMode) particleCount *= 1.5;
      if (activeEffect.type === 'mega' && millis() < activeEffect.until) particleCount *= 2;

      for (let i = 0; i < particleCount; i++) {
        let p = new HeartParticle(mouseX, mouseY);
        if (activeEffect.type === 'mega' && millis() < activeEffect.until) {
          p.r *= 1.5; // 更大的粒子
        }
        particles.push(p);
      }

      // 额外创建一些普通粒子作为装饰效果
      for (let i = 0; i < 10 + combo; i++) {
        let p = new Particle(mouseX, mouseY);
        // 让普通粒子也有一些爱心形状的影响
        p.vel.mult(0.3); // 降低速度
        particles.push(p);
      }
    }
  }




  }
  
  // 自动发射烟花效果 - 可暂停控制
  if (!isAutoFireworkPaused && frameCount % 180 === 0 && particles.length < 300) { // 每隔180帧且未暂停且粒子数量少时自动发射
    let x = random(width);
    let y = random(height * 0.6); // 在画面上半部分发射
    for (let i = 0; i < 15; i++) {
      let p = new Particle(x, y);
      particles.push(p);
    }
  }
  
  // Update and display particles
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();

    // Remove dead particles
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }

  // 自动性能优化：当粒子过多时自动清理部分粒子
  if (particles.length > 800) {
    // 保留最新的300个粒子，清理最老的
    let keepCount = 300;
    if (particles.length > keepCount) {
      particles.splice(0, particles.length - keepCount);
    }
  }
  
  // 计算FPS
  let currentTime = millis();
  if (currentTime - lastFrameTime >= 1000) { // 每秒更新一次FPS
    fps = floor(frameRate());
    lastFrameTime = currentTime;
  }

  // Display game info
  fill(255);
  noStroke();
  textSize(16);
  text("💗 分数: " + score, 20, 30);
  text("🎯 连击: " + combo + "x", 20, 55);
  text("✨ 粒子: " + particles.length, 20, 80);
  text("🎮 FPS: " + fps, 20, 105);
  text("🎈 气球: " + balloonsPoppedCount, 20, 130);

  // 成就状态显示
  let achievementCount = Object.values(achievements).filter(a => a).length;
  text("🏆 成就: " + achievementCount + "/9", 20, 155);

  // 游戏模式显示
  if (gameMode === "timeattack") {
    let currentTimeLeft = timeLeft - (millis() - gameTimer) / 1000;
    if (currentTimeLeft > 0) {
      fill(255, 100, 100);
      textSize(18);
      text("⏰ 时间: " + currentTimeLeft.toFixed(1) + "s", width/2 - 80, 30);
    } else {
      fill(255, 255, 100);
      textSize(20);
      text("⏰ 时间到！最终分数: " + score, width/2 - 120, 30);
      // 时间挑战成就
      if (!achievements.timeAttacker && score >= 200) {
        achievements.timeAttacker = true;
        showAchievement("⚡ 时间挑战者！60秒内获得200分！");
      }
    }
  }

  // 连击提示
  if (combo >= 3) {
    let comboText = "";
    let comboColor = color(255, 200, 100);
    if (combo >= 5) {
      comboText = "🌟 完美连击！";
      comboColor = color(255, 100, 255);
    } else if (combo >= 4) {
      comboText = "⭐ 超神连击！";
      comboColor = color(100, 255, 255);
    } else {
      comboText = "🔥 连击中！";
    }

    fill(comboColor);
    textSize(20);
    text(comboText, width/2 - 60, height - 50);
  }

  // 性能警告
  if (particles.length > 500) {
    fill(255, 100, 100);
    textSize(14);
    text("⚠️ 粒子较多，按空格键可清理", 20, 105);
  } else if (particles.length > 1000) {
    fill(255, 50, 50);
    textSize(14);
    text("🔴 粒子过多，建议清理", 20, 105);
  }

  // 绘制UI按钮
  if (showUI) {
    drawButtons();
  }

  // 绘制特效状态
  drawEffectStatus();
}

// 道具激活函数
function activatePowerUp(type) {
  let duration = 10000; // 10秒持续时间
  activeEffect = { type: type, until: millis() + duration };

  switch(type) {
    case 'double':
      showAchievement("💰 双倍分数激活！10秒内分数翻倍！");
      playClickSound(1000, 0.3);
      break;
    case 'mega':
      showAchievement("💥 超级粒子激活！10秒内粒子效果翻倍！");
      playClickSound(800, 0.3);
      break;
    case 'slow':
      gravity.mult(0.3); // 减慢重力
      showAchievement("🌙 慢动作激活！10秒内粒子飘得更久！");
      playClickSound(600, 0.3);
      setTimeout(() => {
        gravity.div(0.3); // 恢复重力
      }, duration);
      break;
  }
}

// 绘制UI按钮
function drawButtons() {
  if (buttons && buttons.length > 0) {
    for (let button of buttons) {
      // 按钮背景
      fill(0, 0, 0, 150);
      stroke(255);
      strokeWeight(1);
      rect(button.x, button.y, button.w, button.h, 5);

      // 按钮文字
      fill(255);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(12);
      text(button.text, button.x + button.w/2, button.y + button.h/2);
    }
  }
}

// 绘制特效状态
function drawEffectStatus() {
  let yOffset = 160;

  // 显示当前激活的特效
  if (activeEffect.type && millis() < activeEffect.until) {
    let timeLeft = (activeEffect.until - millis()) / 1000;
    fill(255, 255, 0);
    noStroke();
    textSize(14);
    text("⚡ " + getEffectName(activeEffect.type) + " " + timeLeft.toFixed(1) + "s", 20, yOffset);
    yOffset += 25;
  }

  // 显示狂热模式
  if (feverMode) {
    let timeLeft = (feverUntil - millis()) / 1000;
    fill(255, 100, 255);
    noStroke();
    textSize(16);
    text("🔥 狂热模式 " + timeLeft.toFixed(1) + "s", 20, yOffset);
    yOffset += 25;
  }

  // 显示连击计时器
  if (combo > 0) {
    let comboTimeLeft = 2 - (millis() - comboTimer) / 1000;
    if (comboTimeLeft > 0) {
      fill(100, 255, 100);
      noStroke();
      textSize(12);
      text("⏱️ 连击时间: " + comboTimeLeft.toFixed(1) + "s", 20, yOffset);
    }
  }
}

function getEffectName(type) {
  switch(type) {
    case 'double': return "双倍分数";
    case 'mega': return "超级粒子";
    case 'slow': return "慢动作";
    default: return "";
  }
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
  // 重新初始化按钮位置
  initButtons();
  // 重新初始化星星
  stars = [];
  for (let i = 0; i < 50; i++) {
    stars.push(new Star());
  }
}

// 键盘控制功能
function keyPressed() {
  if (key === ' ') { // 空格键 - 清除所有粒子
    particles = [];
    heartsToDraw = [];
    background(0);
    playClickSound(400, 0.2); // 清理音效
  } else if (key === 'r' || key === 'R') { // R键 - 随机位置发射爱心烟花
    let x = random(width);
    let y = random(height * 0.7);
    createHeartFirework(x, y);
    playClickSound(600, 0.15);
  } else if (key === 'c' || key === 'C') { // C键 - 屏幕中央发射爱心烟花
    createHeartFirework(width/2, height/2);
    playClickSound(700, 0.15);
  } else if (key === 's' || key === 'S') { // S键 - 重置分数
    score = 0;
    combo = 0;
    comboTimer = 0;
    // 重置成就
    for (let ach in achievements) {
      achievements[ach] = false;
    }
    showAchievement("🔄 游戏重置！重新开始挑战！");
    playClickSound(300, 0.3);
  } else if (key === 'p' || key === 'P') { // P键 - 暂停/继续自动烟花
    isAutoFireworkPaused = !isAutoFireworkPaused;
    showAchievement(isAutoFireworkPaused ? "⏸️ 自动烟花已暂停" : "▶️ 自动烟花已继续");
    playClickSound(500, 0.2);
  } else if (key === 't' || key === 'T') { // T键 - 切换主题
    currentTheme = (currentTheme + 1) % 3;
    let themeNames = ["粉色主题", "彩虹主题", "金色主题"];
    showAchievement("🎨 切换到" + themeNames[currentTheme] + "！");
    playClickSound(900, 0.2);
  } else if (key === 'm' || key === 'M') { // M键 - 音效开关
    soundEnabled = !soundEnabled;
    showAchievement(soundEnabled ? "🔊 音效已开启" : "🔇 音效已关闭");
  } else if (key === 'h' || key === 'H') { // H键 - 显示/隐藏UI
    showUI = !showUI;
    showAchievement(showUI ? "👁️ UI已显示" : "👁️ UI已隐藏");
  }
}

// 创建爱心烟花的函数
function createHeartFirework(x, y) {
  // 添加临时爱心显示
  heartsToDraw.push({
    x: x,
    y: y,
    size: 3,
    lifetime: 25,
    rotation: random(TWO_PI)
  });

  // 创建爱心烟花粒子
  for (let i = 0; i < 100; i++) {
    let p = new HeartParticle(x, y);
    particles.push(p);
  }

  // 添加装饰粒子
  for (let i = 0; i < 20; i++) {
    let p = new Particle(x, y);
    p.vel.mult(0.5);
    particles.push(p);
  }
}

// 添加双击检测功能
let doubleClickLastTime = 0;
let doubleClickCount = 0;

function mouseClicked() {
  // 检查是否点击了UI按钮
  if (showUI) {
    for (let button of buttons) {
      if (mouseX >= button.x && mouseX <= button.x + button.w &&
          mouseY >= button.y && mouseY <= button.y + button.h) {
        handleButtonClick(button.action);
        return; // 点击了按钮就不执行其他逻辑
      }
    }
  }

  let currentTime = millis();
  if (currentTime - doubleClickLastTime < 300) { // 双击检测（300ms内）
    doubleClickCount++;
    if (doubleClickCount >= 2) {
      // 双击时创建超级爱心烟花
      createSuperHeartFirework(mouseX, mouseY);
      doubleClickCount = 0;
    }
  } else {
    doubleClickCount = 1;
  }
  doubleClickLastTime = currentTime;
}

// 处理按钮点击
function handleButtonClick(action) {
  switch(action) {
    case "reset":
      score = 0;
      combo = 0;
      comboTimer = 0;
      balloonsPoppedCount = 0;
      feverMode = false;
      activeEffect = { type: null, until: 0 };
      // 重置成就
      for (let ach in achievements) {
        achievements[ach] = false;
      }
      showAchievement("🔄 游戏重置！重新开始挑战！");
      playClickSound(300, 0.3);
      break;
    case "theme":
      currentTheme = (currentTheme + 1) % 3;
      let themeNames = ["粉色主题", "彩虹主题", "金色主题"];
      showAchievement("🎨 切换到" + themeNames[currentTheme] + "！");
      playClickSound(900, 0.2);
      break;
    case "sound":
      soundEnabled = !soundEnabled;
      showAchievement(soundEnabled ? "🔊 音效已开启" : "🔇 音效已关闭");
      break;
    case "mode":
      gameMode = gameMode === "freeplay" ? "timeattack" : "freeplay";
      if (gameMode === "timeattack") {
        timeLeft = 60;
        gameTimer = millis();
        showAchievement("⏰ 时间挑战模式！60秒内获得最高分！");
      } else {
        showAchievement("🎮 自由模式！尽情享受烟花吧！");
      }
      playClickSound(700, 0.2);
      break;
    case "toggleUI":
      showUI = !showUI;
      break;
  }
}

// 成就系统函数
function checkAchievements() {
  // 首次点击成就
  if (!achievements.firstClick && score >= 10) {
    achievements.firstClick = true;
    showAchievement("🌟 第一次点击！新手玩家达成！");
  }

  // 三连击成就
  if (!achievements.combo3 && combo >= 3) {
    achievements.combo3 = true;
    showAchievement("🔥 三连击！连击大师！");
  }

  // 五连击成就
  if (!achievements.combo5 && combo >= 5) {
    achievements.combo5 = true;
    showAchievement("🌟 完美五连击！传奇玩家！");
  }

  // 分数成就
  if (!achievements.score100 && score >= 100) {
    achievements.score100 = true;
    showAchievement("💯 分数破百！人气王！");
  }

  if (!achievements.score500 && score >= 500) {
    achievements.score500 = true;
    showAchievement("🏆 分数破500！烟花大师！");
  }

  // 气球射手成就
  if (!achievements.balloonPopper && balloonsPoppedCount >= 10) {
    achievements.balloonPopper = true;
    showAchievement("🎈 气球射手！击破10个气球！");
  }

  // 狂热大师成就
  if (!achievements.feverMaster && feverMode) {
    achievements.feverMaster = true;
    showAchievement("🔥 狂热大师！进入狂热模式！");
  }
}

function showAchievement(message) {
  // 创建成就提示
  const achievement = {
    message: message,
    lifetime: 180, // 3秒显示
    y: height / 2
  };

  // 显示成就提示（临时添加到heartsToDraw中，复用显示逻辑）
  heartsToDraw.push({
    x: width / 2,
    y: achievement.y,
    size: 0.1,
    lifetime: achievement.lifetime,
    rotation: 0,
    isAchievement: true,
    message: message
  });
}

// 创建超级爱心烟花
function createSuperHeartFirework(x, y) {
  // 播放超级音效
  playSuperSound();

  // 超级烟花成就
  if (!achievements.superFirework) {
    achievements.superFirework = true;
    showAchievement("💥 超级烟花！特效大师！");
  }

  // 创建多个旋转的爱心
  for (let i = 0; i < 8; i++) {
    heartsToDraw.push({
      x: x,
      y: y,
      size: 4,
      lifetime: 30,
      rotation: i * PI/4 // 每个爱心旋转不同角度
    });
  }

  // 创建大量爱心粒子
  for (let i = 0; i < 200; i++) {
    let p = new HeartParticle(x, y);
    // 让超级烟花的粒子更大更快
    p.r = random(3, 8);
    p.vel.mult(1.5);
    particles.push(p);
  }
}