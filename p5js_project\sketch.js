// 普通粒子类
class Particle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    // 随机速度，让烟花效果更丰富
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    // 随机颜色
    this.color = color(random(255), random(255), random(255));
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    // 设置颜色和透明度
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 爱心形状粒子类
class HeartParticle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    // 爱心形状的速度分布
    this.createHeartVelocity();
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 300; // 增加生命周期让粒子存活更久
    // 丰富多彩的爱心颜色主题
    let colorType = floor(random(4)); // 随机选择颜色类型
    switch(colorType) {
      case 0: // 粉红色系
        this.color = color(random(255, 255), random(50, 150), random(100, 200));
        break;
      case 1: // 红色系
        this.color = color(random(200, 255), random(0, 50), random(0, 50));
        break;
      case 2: // 紫红色系
        this.color = color(random(200, 255), random(0, 100), random(100, 200));
        break;
      case 3: // 金色系（特殊效果）
        this.color = color(random(255, 255), random(150, 255), random(0, 100));
        break;
    }
  }
  
  // 创建爱心形状的速度分布
  createHeartVelocity() {
    // 使用参数方程生成爱心形状的速度分布
    let t = random(TWO_PI);
    let scale = random(3, 8); // 增加规模范围让爱心更大
    // 爱心参数方程: x = 16sin³(t), y = 13cos(t) - 5cos(2t) - 2cos(3t) - cos(4t)
    let x = 16 * pow(sin(t), 3);
    let y = -(13 * cos(t) - 5 * cos(2*t) - 2 * cos(3*t) - cos(4*t));
    this.vel = createVector(x * scale / 30, y * scale / 30); // 调整缩放比例

    // 添加一些随机性，使烟花效果更自然
    this.vel.add(random(-1, 1), random(-1, 1));

    // 根据位置调整速度，让爱心形状更清晰
    let speed = this.vel.mag();
    if (speed > 0) {
      // 让粒子速度更均匀
      this.vel.setMag(random(2, 6));
    }
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
  }

  show() {
    // 设置颜色和透明度
    stroke(red(this.color), green(this.color), blue(this.color), this.lifetime);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 全局变量
let particles = [];
let gravity;
let heartsToDraw = []; // 用于跟踪爱心绘制
let lastClickTime = 0; // 记录上次点击时间，防止过于频繁的点击
let score = 0; // 爱心收集分数
let combo = 0; // 连击计数
let comboTimer = 0; // 连击计时器
let isAutoFireworkPaused = false; // 自动烟花暂停状态

// 音效系统
let audioContext;
let soundEnabled = true;
let achievements = {
  firstClick: false,
  combo3: false,
  combo5: false,
  score100: false,
  score500: false,
  superFirework: false
};
let currentTheme = 0; // 0: 粉色主题, 1: 彩虹主题, 2: 金色主题
let lastFrameTime = 0;
let fps = 0;

// 音效系统函数
function initAudio() {
  try {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  } catch (e) {
    console.log('Web Audio API not supported');
  }
}

function playClickSound(frequency = 800, duration = 0.1) {
  if (!soundEnabled || !audioContext) return;

  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  } catch (e) {
    console.log('Error playing sound');
  }
}

function playComboSound(level) {
  if (!soundEnabled) return;

  // 根据连击等级播放不同音效
  switch(level) {
    case 3:
      playClickSound(1000, 0.2); // 三连击
      setTimeout(() => playClickSound(1200, 0.2), 100);
      break;
    case 5:
      playClickSound(800, 0.3); // 五连击
      setTimeout(() => playClickSound(1000, 0.3), 150);
      setTimeout(() => playClickSound(1200, 0.3), 300);
      break;
  }
}

function playSuperSound() {
  if (!soundEnabled) return;

  // 超级烟花音效
  playClickSound(600, 0.4);
  setTimeout(() => playClickSound(800, 0.3), 200);
  setTimeout(() => playClickSound(1000, 0.3), 400);
  setTimeout(() => playClickSound(1200, 0.2), 600);
}

// 主题系统
function getThemeColor(baseColor) {
  switch(currentTheme) {
    case 0: // 粉色主题
      return baseColor;
    case 1: // 彩虹主题
      return color(random(255), random(255), random(255));
    case 2: // 金色主题
      return color(random(200, 255), random(150, 255), random(0, 100));
    default:
      return baseColor;
  }
}

// 绘制爱心形状
function drawHeart(x, y, size, rotation = 0, isAchievement = false, message = "") {
  push();
  translate(x, y);
  rotate(rotation); // 添加旋转效果
  scale(size);

  if (isAchievement) {
    // 成就通知显示
    fill(255, 255, 100, 200);
    stroke(255, 200, 0, 255);
    strokeWeight(3);
    rect(-150, -30, 300, 60, 10);

    fill(0);
    noStroke();
    textSize(16);
    textAlign(CENTER, CENTER);
    text(message, 0, 0);
  } else {
    // 创建渐变色爱心效果 - 支持主题系统
    let heartColor = getThemeColor(color(255, 50, 100, 180)); // 粉红色
    let strokeColor = getThemeColor(color(255, 100, 150, 200)); // 浅粉色描边

    fill(heartColor);
    stroke(strokeColor);
    strokeWeight(2);

    beginShape();
    for (let i = 0; i < TWO_PI; i += 0.05) { // 更平滑的曲线
      let px = 16 * pow(sin(i), 3);
      let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i));
      vertex(px, py);
    }
    endShape(CLOSE);

    // 添加爱心内部的小亮点效果
    let highlightColor = getThemeColor(color(255, 150, 200, 100));
    fill(highlightColor);
    noStroke();
    ellipse(0, -2, 6, 6); // 爱心上方的亮点
  }

  pop();
}

function setup() {
  createCanvas(windowWidth, windowHeight);
  gravity = createVector(0, 0.05); // 减小重力影响，让粒子飘得更久
  background(0);

  // 初始化音频系统
  initAudio();

  // 监听用户交互来激活音频上下文（浏览器要求）
  const startAudio = () => {
    if (audioContext && audioContext.state === 'suspended') {
      audioContext.resume();
    }
    document.removeEventListener('click', startAudio);
    document.removeEventListener('touchstart', startAudio);
  };
  document.addEventListener('click', startAudio);
  document.addEventListener('touchstart', startAudio);
}

function draw() {
  background(0, 25);
  
  // 绘制临时爱心和成就通知
  for (let i = heartsToDraw.length - 1; i >= 0; i--) {
    let heart = heartsToDraw[i];

    if (heart.isAchievement) {
      // 成就通知 - 位置固定在屏幕中央，不随生命周期变化
      drawHeart(heart.x, heart.y, heart.size, heart.rotation, true, heart.message);
    } else {
      // 普通爱心 - 让爱心在生命周期中逐渐变大并旋转
      let currentSize = heart.size * (1 + (20 - heart.lifetime) / 20);
      let currentRotation = heart.rotation + (20 - heart.lifetime) * 0.1;
      drawHeart(heart.x, heart.y, currentSize, currentRotation);
    }

    heart.lifetime--;
    if (heart.lifetime <= 0) {
      heartsToDraw.splice(i, 1);
    }
  }
  
  // 鼠标点击时创建爱心烟花效果
  if (mouseIsPressed && millis() - lastClickTime > 100) { // 防止过于频繁的点击
    lastClickTime = millis();

    // 播放点击音效
    playClickSound(800 + combo * 50, 0.15);

    // 连击系统
    if (millis() - comboTimer < 2000) { // 2秒内点击算连击
      combo++;
      if (combo > 5) combo = 5; // 最大连击数限制

      // 连击音效
      if (combo === 3) playComboSound(3);
      else if (combo === 5) playComboSound(5);
    } else {
      combo = 1;
    }
    comboTimer = millis();

    // 成就检查
    checkAchievements();

    // 根据连击数计算分数
    let baseScore = 10;
    let comboBonus = combo * 2;
    score += baseScore + comboBonus;

    // 添加一个临时爱心显示
    heartsToDraw.push({
      x: mouseX,
      y: mouseY,
      size: 2 + combo * 0.2, // 连击时爱心变大
      lifetime: 20,
      rotation: random(TWO_PI) // 添加随机旋转
    });

    // 创建爱心烟花效果 - 根据连击数调整粒子数量
    let particleCount = 60 + combo * 10;
    for (let i = 0; i < particleCount; i++) {
      let p = new HeartParticle(mouseX, mouseY);
      particles.push(p);
    }

    // 额外创建一些普通粒子作为装饰效果
    for (let i = 0; i < 10 + combo; i++) {
      let p = new Particle(mouseX, mouseY);
      // 让普通粒子也有一些爱心形状的影响
      p.vel.mult(0.3); // 降低速度
      particles.push(p);
    }
  }
  
  // 自动发射烟花效果 - 可暂停控制
  if (!isAutoFireworkPaused && frameCount % 180 === 0 && particles.length < 300) { // 每隔180帧且未暂停且粒子数量少时自动发射
    let x = random(width);
    let y = random(height * 0.6); // 在画面上半部分发射
    for (let i = 0; i < 15; i++) {
      let p = new Particle(x, y);
      particles.push(p);
    }
  }
  
  // Update and display particles
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();

    // Remove dead particles
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }

  // 自动性能优化：当粒子过多时自动清理部分粒子
  if (particles.length > 800) {
    // 保留最新的300个粒子，清理最老的
    let keepCount = 300;
    if (particles.length > keepCount) {
      particles.splice(0, particles.length - keepCount);
    }
  }
  
  // 计算FPS
  let currentTime = millis();
  if (currentTime - lastFrameTime >= 1000) { // 每秒更新一次FPS
    fps = floor(frameRate());
    lastFrameTime = currentTime;
  }

  // Display game info
  fill(255);
  noStroke();
  textSize(16);
  text("💗 分数: " + score, 20, 30);
  text("🎯 连击: " + combo + "x", 20, 55);
  text("✨ 粒子: " + particles.length, 20, 80);
  text("🎮 FPS: " + fps, 20, 105);

  // 成就状态显示
  let achievementCount = Object.values(achievements).filter(a => a).length;
  text("🏆 成就: " + achievementCount + "/6", 20, 130);

  // 连击提示
  if (combo >= 3) {
    let comboText = "";
    let comboColor = color(255, 200, 100);
    if (combo >= 5) {
      comboText = "🌟 完美连击！";
      comboColor = color(255, 100, 255);
    } else if (combo >= 4) {
      comboText = "⭐ 超神连击！";
      comboColor = color(100, 255, 255);
    } else {
      comboText = "🔥 连击中！";
    }

    fill(comboColor);
    textSize(20);
    text(comboText, width/2 - 60, height - 50);
  }

  // 性能警告
  if (particles.length > 500) {
    fill(255, 100, 100);
    textSize(14);
    text("⚠️ 粒子较多，按空格键可清理", 20, 105);
  } else if (particles.length > 1000) {
    fill(255, 50, 50);
    textSize(14);
    text("🔴 粒子过多，建议清理", 20, 105);
  }
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}

// 键盘控制功能
function keyPressed() {
  if (key === ' ') { // 空格键 - 清除所有粒子
    particles = [];
    heartsToDraw = [];
    background(0);
    playClickSound(400, 0.2); // 清理音效
  } else if (key === 'r' || key === 'R') { // R键 - 随机位置发射爱心烟花
    let x = random(width);
    let y = random(height * 0.7);
    createHeartFirework(x, y);
    playClickSound(600, 0.15);
  } else if (key === 'c' || key === 'C') { // C键 - 屏幕中央发射爱心烟花
    createHeartFirework(width/2, height/2);
    playClickSound(700, 0.15);
  } else if (key === 's' || key === 'S') { // S键 - 重置分数
    score = 0;
    combo = 0;
    comboTimer = 0;
    // 重置成就
    for (let ach in achievements) {
      achievements[ach] = false;
    }
    showAchievement("🔄 游戏重置！重新开始挑战！");
    playClickSound(300, 0.3);
  } else if (key === 'p' || key === 'P') { // P键 - 暂停/继续自动烟花
    isAutoFireworkPaused = !isAutoFireworkPaused;
    showAchievement(isAutoFireworkPaused ? "⏸️ 自动烟花已暂停" : "▶️ 自动烟花已继续");
    playClickSound(500, 0.2);
  } else if (key === 't' || key === 'T') { // T键 - 切换主题
    currentTheme = (currentTheme + 1) % 3;
    let themeNames = ["粉色主题", "彩虹主题", "金色主题"];
    showAchievement("🎨 切换到" + themeNames[currentTheme] + "！");
    playClickSound(900, 0.2);
  } else if (key === 'm' || key === 'M') { // M键 - 音效开关
    soundEnabled = !soundEnabled;
    showAchievement(soundEnabled ? "🔊 音效已开启" : "🔇 音效已关闭");
  }
}

// 创建爱心烟花的函数
function createHeartFirework(x, y) {
  // 添加临时爱心显示
  heartsToDraw.push({
    x: x,
    y: y,
    size: 3,
    lifetime: 25,
    rotation: random(TWO_PI)
  });

  // 创建爱心烟花粒子
  for (let i = 0; i < 100; i++) {
    let p = new HeartParticle(x, y);
    particles.push(p);
  }

  // 添加装饰粒子
  for (let i = 0; i < 20; i++) {
    let p = new Particle(x, y);
    p.vel.mult(0.5);
    particles.push(p);
  }
}

// 添加双击检测功能
let doubleClickLastTime = 0;
let doubleClickCount = 0;

function mouseClicked() {
  let currentTime = millis();
  if (currentTime - doubleClickLastTime < 300) { // 双击检测（300ms内）
    doubleClickCount++;
    if (doubleClickCount >= 2) {
      // 双击时创建超级爱心烟花
      createSuperHeartFirework(mouseX, mouseY);
      doubleClickCount = 0;
    }
  } else {
    doubleClickCount = 1;
  }
  doubleClickLastTime = currentTime;
}

// 成就系统函数
function checkAchievements() {
  // 首次点击成就
  if (!achievements.firstClick && score >= 10) {
    achievements.firstClick = true;
    showAchievement("🌟 第一次点击！新手玩家达成！");
  }

  // 三连击成就
  if (!achievements.combo3 && combo >= 3) {
    achievements.combo3 = true;
    showAchievement("🔥 三连击！连击大师！");
  }

  // 五连击成就
  if (!achievements.combo5 && combo >= 5) {
    achievements.combo5 = true;
    showAchievement("🌟 完美五连击！传奇玩家！");
  }

  // 分数成就
  if (!achievements.score100 && score >= 100) {
    achievements.score100 = true;
    showAchievement("💯 分数破百！人气王！");
  }

  if (!achievements.score500 && score >= 500) {
    achievements.score500 = true;
    showAchievement("🏆 分数破500！烟花大师！");
  }
}

function showAchievement(message) {
  // 创建成就提示
  const achievement = {
    message: message,
    lifetime: 180, // 3秒显示
    y: height / 2
  };

  // 显示成就提示（临时添加到heartsToDraw中，复用显示逻辑）
  heartsToDraw.push({
    x: width / 2,
    y: achievement.y,
    size: 0.1,
    lifetime: achievement.lifetime,
    rotation: 0,
    isAchievement: true,
    message: message
  });
}

// 创建超级爱心烟花
function createSuperHeartFirework(x, y) {
  // 播放超级音效
  playSuperSound();

  // 超级烟花成就
  if (!achievements.superFirework) {
    achievements.superFirework = true;
    showAchievement("💥 超级烟花！特效大师！");
  }

  // 创建多个旋转的爱心
  for (let i = 0; i < 8; i++) {
    heartsToDraw.push({
      x: x,
      y: y,
      size: 4,
      lifetime: 30,
      rotation: i * PI/4 // 每个爱心旋转不同角度
    });
  }

  // 创建大量爱心粒子
  for (let i = 0; i < 200; i++) {
    let p = new HeartParticle(x, y);
    // 让超级烟花的粒子更大更快
    p.r = random(3, 8);
    p.vel.mult(1.5);
    particles.push(p);
  }
}