<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>P5.js Interactive Particle System</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    .instructions {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 15px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-width: 300px;
    }

    .instructions h3 {
      margin: 0 0 10px 0;
      color: #ff69b4;
    }

    .instructions ul {
      margin: 0;
      padding-left: 20px;
    }

    .instructions li {
      margin: 5px 0;
    }
  </style>
</head>
<body>
      <div class="instructions">
    <h3>💗 爱心烟花游戏 💗</h3>
    <ul>
      <li><strong>鼠标点击</strong>: 释放爱心烟花 ✨</li>
      <li><strong>点击气球</strong>: 获得额外分数 🎈</li>
      <li><strong>快速连击</strong>: 获得连击加分 🔥</li>
      <li><strong>空格键</strong>: 清除所有烟花 🧹</li>
      <li><strong>R键</strong>: 随机位置发射烟花 🎲</li>
      <li><strong>C键</strong>: 屏幕中央发射烟花 🎯</li>
      <li><strong>S键</strong>: 重置分数 🔄</li>
    </ul>
    <p style="margin: 10px 0 0 0; font-size: 12px; color: #ff69b4;">
      💝 快来和小朋友一起玩爱心烟花吧！💝<br>
      🎯 目标：击破气球，获得高分和连击！<br>
      🎈 彩色气球会定期出现，点击获得额外奖励！<br>
      ⭐ 背景中的星星会闪烁，营造梦幻氛围！<br>
      🔥 连续快速点击可以获得连击奖励！
    </p>
  </div>
  <script src="sketch_simple.js"></script>
</body>
</html>