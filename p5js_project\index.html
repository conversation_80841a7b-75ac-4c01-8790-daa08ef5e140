<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>P5.js Interactive Particle System</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    .instructions {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 15px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-width: 300px;
    }

    .instructions h3 {
      margin: 0 0 10px 0;
      color: #ff69b4;
    }

    .instructions ul {
      margin: 0;
      padding-left: 20px;
    }

    .instructions li {
      margin: 5px 0;
    }
  </style>
</head>
<body>
      <div class="instructions">
    <h3>💗 爱心烟花游戏 💗</h3>
    <ul>
      <li><strong>鼠标点击</strong>: 释放爱心烟花 ✨</li>
      <li><strong>点击气球</strong>: 获得额外分数 🎈</li>
      <li><strong>点击道具</strong>: 激活特殊效果 ⚡</li>
      <li><strong>快速连击</strong>: 获得连击加分和狂热模式 🔥</li>
      <li><strong>鼠标双击</strong>: 超级爱心烟花特效 🌟</li>
      <li><strong>空格键</strong>: 清除所有烟花 🧹</li>
      <li><strong>R键</strong>: 随机位置发射烟花 🎲</li>
      <li><strong>C键</strong>: 屏幕中央发射烟花 🎯</li>
      <li><strong>H键</strong>: 显示/隐藏UI按钮 👁️</li>
    </ul>
    <h4>🎮 游戏模式</h4>
    <ul>
      <li><strong>自由模式</strong>: 尽情享受烟花效果</li>
      <li><strong>时间挑战</strong>: 60秒内获得最高分</li>
    </ul>
    <h4>⚡ 道具效果</h4>
    <ul>
      <li><strong>金星(2X)</strong>: 双倍分数 10秒</li>
      <li><strong>紫圆(MEGA)</strong>: 超级粒子效果 10秒</li>
      <li><strong>青方(SLOW)</strong>: 慢动作效果 10秒</li>
    </ul>
    <p style="margin: 10px 0 0 0; font-size: 12px; color: #ff69b4;">
      💝 快来和小朋友一起玩爱心烟花吧！💝<br>
      🎯 目标：收集成就，击破气球，获得高分！<br>
      🏆 共有9个成就等你解锁！<br>
      🎈 气球会定期出现，点击获得额外奖励！<br>
      ⚡ 道具会偶尔出现，激活强力特效！
    </p>
  </div>
  <script src="sketch.js"></script>
</body>
</html>